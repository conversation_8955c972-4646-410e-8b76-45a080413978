# -*- coding: utf-8 -*-
"""
异步版本的银行相关API
用于替代传统的Django View，解决连接池泄漏问题
"""

from ninja import Router
from ninja.security import HttpBearer
from django.http import JsonResponse
from django.utils import timezone
from django.core.cache import cache
from datetime import timedelta
# 移除sync_to_async导入，现在完全使用异步工具函数
import logging
import json
import calendar
import pytz
import time
import functools
import re

from api.ninja_apis.async_utils import (
    get_async, save_async, filter_async, filter_first_async, get_or_create_async,
    update_async, exists_async, count_async, many_to_many_all_async,
    many_to_many_clear_async, many_to_many_add_async, filter_with_annotations_async,
    filter_with_select_related_async, run_sync_function_async, create_async
)
from api.models import (
    UserInfo, Activity, BadHabitRecord, AbstractGoal, AbstractGoalRecord,
    UserGoal, UserCard, UserActivity
)
from api.views.content_review import HuaweiContentReview
from django.conf import settings

# 导入每日中医题目路由器
from api.ninja_apis.daily_tcm_question_apis import daily_tcm_router

logger = logging.getLogger(__name__)
from api.utils.rate_limiter import rate_limit

def api_timer(func_name=None):
    """
    API耗时计算装饰器
    打印API执行时间，用于性能监控和优化

    Args:
        func_name: 自定义函数名，如果不提供则使用实际函数名
    """
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            name = func_name or func.__name__
            print(f"[API_TIMER] 🚀 {name} 开始执行")

            try:
                result = await func(*args, **kwargs)
                end_time = time.time()
                duration = end_time - start_time
                print(f"[API_TIMER] ✅ {name} 执行完成，耗时: {duration:.3f}秒")

                # 新增：记录到性能日志（保持完全兼容）
                try:
                    import logging
                    performance_logger = logging.getLogger('api_performance')
                    performance_logger.info(f"API_SUCCESS|{name}|{duration:.3f}")
                except:
                    pass  # 静默失败，不影响原有功能

                return result
            except Exception as e:
                end_time = time.time()
                duration = end_time - start_time
                print(f"[API_TIMER] ❌ {name} 执行失败，耗时: {duration:.3f}秒，错误: {str(e)}")

                # 新增：记录到性能日志（保持完全兼容）
                try:
                    import logging
                    performance_logger = logging.getLogger('api_performance')
                    performance_logger.error(f"API_ERROR|{name}|{duration:.3f}|{str(e)}")
                except:
                    pass  # 静默失败，不影响原有功能

                raise

        return wrapper
    return decorator

# 创建异步银行API路由器
async_bank_router = Router()

class AsyncTokenAuth(HttpBearer):
    """异步Token认证"""
    def authenticate(self, request, token):
        # 这里使用中间件设置的user_id
        user_id = getattr(request, 'user_id', None)
        return user_id if user_id else None

auth = AsyncTokenAuth()

async def check_membership_status_async(user):
    """异步检查会员状态"""
    now = timezone.now()
    should_save = False
    
    # 使用结束日期判断会员状态
    if user.is_member and user.membership_end_date:
        if now > user.membership_end_date:
            user.is_member = False
            user.member_type = 'none'
            user.member_duration = timedelta(days=0)
            should_save = True
    # 兼容旧的判断方式
    elif user.is_member and user.last_payment_time:
        elapsed_time = now - user.last_payment_time
        remaining_duration = user.member_duration - elapsed_time
        
        if remaining_duration.total_seconds() <= 0:
            user.is_member = False
            user.member_type = 'none'
            user.member_duration = timedelta(days=0)
            user.membership_end_date = None
            should_save = True
    
    if should_save:
        await save_async(user)
                
    return user.is_member

async def get_remaining_days_async(user):
    """异步获取剩余会员天数"""
    now = timezone.now()
    
    # 优先使用结束日期计算
    if user.is_member and user.membership_end_date:
        remaining = user.membership_end_date - now
        return max(0, remaining.days)
    
    # 兼容旧的计算方式
    elif user.is_member and user.last_payment_time:
        elapsed_time = now - user.last_payment_time
        remaining_duration = user.member_duration - elapsed_time
        return max(0, remaining_duration.days)
        
    return 0

@async_bank_router.post("/checkmembership/")
@rate_limit("检查会员状态", normal_limit=300, member_limit=1000)
@api_timer("检查会员状态")
async def check_membership_async(request):
    """异步检查会员状态 - 替代checkmembershipView"""
    user_id = getattr(request, 'user_id', None)
    
    if not user_id:
        return JsonResponse({'error': '无法验证用户身份'}, status=401)

    cache_key = f"checkmembership_async:{user_id}"
    
    try:
        cached_data_str = cache.get(cache_key)
        if cached_data_str:
            try:
                cached_data = json.loads(cached_data_str)
                logger.info(f"[CACHE_HIT] ✅ {cache_key} - 返回用户 {user_id} 的缓存数据")
                return JsonResponse(cached_data)
            except json.JSONDecodeError:
                logger.warning(f"[CACHE_ERROR] ⚠️ {cache_key} - 解析用户 {user_id} 的缓存数据失败，将重新获取")
    except Exception as e:
        logger.error(f"[CACHE_ERROR] ⚠️ 读取缓存 {cache_key} 失败: {str(e)}，将重新获取")

    try:
        # 使用异步查询
        user = await get_async(UserInfo, id=user_id)
        if not user:
            return JsonResponse({'error': '用户不存在'}, status=404)
        
        # 异步检查会员状态
        await check_membership_status_async(user)
        
        # 计算剩余天数
        remaining_days = await get_remaining_days_async(user)
        
        response_data = {
            'ifmember': user.is_member,
            'memberlastingtime': user.member_duration.total_seconds() if user.member_duration else None,
            'exp': user.EXP,
            'level': user.Level,
            'score': user.score,
            'member_days': remaining_days,
            'nickname': user.nickname,
            'avatar': user.avatar,
            'memberLevel': user.member_Level,
            'memberexp': user.member_EXP,
            'health_score': user.health_score,
            'consecutive_login_days': user.consecutive_login_days,
            'total_login_days': user.total_login_days,
            'check_in_count': user.check_in_count,
            'membership_end_date': user.membership_end_date.isoformat() if user.membership_end_date else None,
            'member_type': user.member_type,
            'login_method': user.login_method,
            'last_payment_time': user.last_payment_time.isoformat() if user.last_payment_time else None
        }
        
        try:
            cache.set(cache_key, json.dumps(response_data), timeout=300) # 缓存5分钟
            logger.info(f"[CACHE_SET] 💾 {cache_key} - 用户 {user_id} 的数据已缓存")
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 写入缓存 {cache_key} 失败: {str(e)}")
            
        return JsonResponse(response_data)
        
    except Exception as e:
        logger.error(f"异步检查会员状态失败: {str(e)}")
        return JsonResponse({'error': '服务器错误'}, status=500)

@async_bank_router.post("/empty-request/")
@api_timer("空请求")
async def empty_request_async(request):
    """异步空请求 - 替代EmptyRequestView"""
    logger.info('异步空请求以刷新token')
    return {"message": "Empty request OK"}

@async_bank_router.get("/user-goal/")
@api_timer("获取用户目标")
async def user_goal_async(request):
    """异步用户目标查询 - 替代UserGoalView"""
    user_id = getattr(request, 'user_id', None)
    
    if not user_id:
        return JsonResponse({'error': '无法验证用户身份'}, status=401)
    
    try:
        # 使用异步查询用户目标
        goals = await filter_async(AbstractGoal, user_id=user_id)
        
        goals_data = []
        for goal in goals:
            goals_data.append({
                'id': goal.id,
                'goal_name': goal.goal_name,
                'description': goal.description,
                'target_days': goal.target_days,
                'current_streak': goal.current_streak,
                'longest_streak': goal.longest_streak,
                'is_completed': goal.is_completed,
                'created_at': goal.created_at.isoformat() if goal.created_at else None
            })
        
        return JsonResponse(goals_data, safe=False)
        
    except Exception as e:
        logger.error(f"异步查询用户目标失败: {str(e)}")
        return JsonResponse({'error': '服务器错误'}, status=500)

@async_bank_router.get("/activity-list/")
@api_timer("获取活动列表")
async def activity_list_async(request):
    """异步查询活动列表"""
    try:
        user_id = getattr(request, 'user_id', None)
        if not user_id:
            return {"error": "访问需要权限"}

        cache_key = f"activity_list_async:{user_id}"
        try:
            # 缓存操作保持同步
            cached_data_str = cache.get(cache_key)
            if cached_data_str:
                try:
                    cached_data = json.loads(cached_data_str)
                    logger.info(f"[CACHE_HIT] ✅ {cache_key} - 返回用户 {user_id} 的活动列表缓存数据")
                    return cached_data
                except json.JSONDecodeError:
                    logger.warning(f"[CACHE_ERROR] ⚠️ {cache_key} - 解析缓存数据失败，将重新获取")
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 读取缓存 {cache_key} 失败: {str(e)}，将重新获取")
        
        # 使用新的异步工具查询，不再使用sync_to_async
        # 获取系统预设活动
        system_activities = await filter_async(
            Activity, 
            is_custom=False, 
            is_bad_habit=False
        )
        
        # 获取用户自定义活动
        custom_activities = await filter_async(
            Activity,
            created_by=user_id,
            is_bad_habit=False
        )
        
        # 合并结果，确保自定义活动排在前面，并去除重复活动
        activities_data = []
        activity_names_lower = set()
        
        # 先添加用户自定义活动
        for activity in custom_activities:
            activity_name_lower = activity.name.strip().lower()
            if activity_name_lower not in activity_names_lower:
                activity_names_lower.add(activity_name_lower)
                activities_data.append({
                    'id': activity.id,
                    'name': activity.name,
                    'icon': activity.icon,
                    'description': activity.description,
                    'is_custom': True
                })
        
        # 再添加系统预设活动（排除已添加的同名活动）
        for activity in system_activities:
            activity_name_lower = activity.name.strip().lower()
            if activity_name_lower not in activity_names_lower:
                activity_names_lower.add(activity_name_lower)
                activities_data.append({
                    'id': activity.id,
                    'name': activity.name,
                    'icon': activity.icon,
                    'description': activity.description,
                    'is_custom': False
                })
        
        try:
            # 缓存操作保持同步
            cache.set(cache_key, json.dumps(activities_data), timeout=900) # 缓存15分钟
            logger.info(f"[CACHE_SET] 💾 {cache_key} - 用户 {user_id} 的活动列表数据已缓存")
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 写入缓存 {cache_key} 失败: {str(e)}")

        return activities_data
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        logger.error(f"异步查询活动列表失败: {str(e)}")
        return {"error": "查询活动列表失败"}

@async_bank_router.get("/badhabit-list/")
@api_timer("获取坏习惯列表")
async def badhabit_list_async(request):
    """异步查询坏习惯列表"""
    try:
        user_id = getattr(request, 'user_id', None)
        if not user_id:
            return {"error": "访问需要权限"}

        cache_key = f"badhabit_list_async:{user_id}"
        try:
            cached_data_str = cache.get(cache_key)
            if cached_data_str:
                try:
                    cached_data = json.loads(cached_data_str)
                    logger.info(f"[CACHE_HIT] ✅ {cache_key} - 返回用户 {user_id} 的坏习惯列表缓存数据")
                    return cached_data
                except json.JSONDecodeError:
                    logger.warning(f"[CACHE_ERROR] ⚠️ {cache_key} - 解析缓存数据失败，将重新获取")
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 读取缓存 {cache_key} 失败: {str(e)}，将重新获取")
        
        # 获取系统预设坏习惯 - 使用新的异步工具
        system_habits = await filter_async(
            Activity,
            is_custom=False,
            is_bad_habit=True
        )
        
        # 获取用户自定义坏习惯 - 使用新的异步工具
        custom_habits = await filter_async(
            Activity,
            created_by=user_id,
            is_bad_habit=True
        )
        
        # 合并结果，确保自定义坏习惯排在前面
        habits_data = []
        
        # 先添加用户自定义坏习惯
        for habit in custom_habits:
            habits_data.append({
                'id': habit.id,
                'name': habit.name,
                'icon': habit.icon,
                'description': habit.description,
                'is_custom': True
            })
            
        # 再添加系统预设坏习惯
        for habit in system_habits:
            habits_data.append({
                'id': habit.id,
                'name': habit.name,
                'icon': habit.icon,
                'description': habit.description,
                'is_custom': False
            })
            
        try:
            cache.set(cache_key, json.dumps(habits_data), timeout=900) # 缓存15分钟
            logger.info(f"[CACHE_SET] 💾 {cache_key} - 用户 {user_id} 的坏习惯列表数据已缓存")
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 写入缓存 {cache_key} 失败: {str(e)}")

        return habits_data
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        logger.error(f"异步查询坏习惯列表失败: {str(e)}")
        return {"error": "查询坏习惯列表失败"}

@async_bank_router.get("/user-stats/")
@api_timer("获取用户统计信息")
async def user_stats_async(request):
    """异步查询用户统计信息"""
    try:
        user_id = getattr(request, 'user_id', None)
        if not user_id:
            return {"error": "访问需要权限"}

        cache_key = f"user_stats_async:{user_id}"
        try:
            cached_data_str = cache.get(cache_key)
            if cached_data_str:
                try:
                    cached_data = json.loads(cached_data_str)
                    logger.info(f"[CACHE_HIT] ✅ {cache_key} - 返回用户 {user_id} 的统计信息缓存数据")
                    return cached_data
                except json.JSONDecodeError:
                    logger.warning(f"[CACHE_ERROR] ⚠️ {cache_key} - 解析缓存数据失败，将重新获取")
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 读取缓存 {cache_key} 失败: {str(e)}，将重新获取")
        
        # 获取用户信息
        user = await get_async(UserInfo, id=user_id)
        
        # 获取用户的所有目标 - 修复：移除is_active字段
        goals = await filter_async(UserGoal, user_id=user_id)
        
        # 获取用户的抽象目标 - 修复：移除is_active字段
        abstract_goals = await filter_async(AbstractGoal, user_id=user_id)
        
        # 获取用户的打卡记录
        today = timezone.now().date()
        last_30_days = today - timezone.timedelta(days=30)
        
        user_cards = await filter_async(UserCard, user_id=user_id, date__gte=last_30_days)
        
        # 计算连续打卡天数
        current_streak = 0
        for i in range(30):
            check_date = today - timezone.timedelta(days=i)
            card_exists = any(card.date == check_date for card in user_cards)
            
            if card_exists:
                current_streak += 1
            else:
                break
                
        # 获取用户最常打卡的活动
        from django.db.models import Count
        user_activities = await filter_with_annotations_async(
            UserActivity,
            user_id=user_id,
            date__gte=last_30_days,
            completed=True,
            annotations={'count': Count('id')},
            values_fields=['activity__name', 'count'],
            order_by=['-count']
        )

        top_activities = [
            {'name': ua['activity__name'], 'count': ua['count']}
            for ua in user_activities[:5]
        ]
        
        # 获取用户成功避免的坏习惯
        avoided_habits = await filter_with_annotations_async(
            BadHabitRecord,
            user_id=user_id,
            date__gte=last_30_days,
            avoided=True,
            annotations={'count': Count('id')},
            values_fields=['habit__name', 'count'],
            order_by=['-count']
        )

        top_avoided_habits = [
            {'name': ah['habit__name'], 'count': ah['count']}
            for ah in avoided_habits[:5]
        ]

        # 获取用户未能避免的坏习惯
        failed_habits = await filter_with_annotations_async(
            BadHabitRecord,
            user_id=user_id,
            date__gte=last_30_days,
            avoided=False,
            annotations={'count': Count('id')},
            values_fields=['habit__name', 'count'],
            order_by=['-count']
        )

        top_failed_habits = [
            {'name': fh['habit__name'], 'count': fh['count']}
            for fh in failed_habits[:5]
        ]
        
        # 构建返回数据
        goals_data = []
        for goal in goals:
            # 计算目标进度
            progress = min(100, int((goal.current_streak / goal.target_days) * 100)) if goal.target_days > 0 else 0
            
            goals_data.append({
                'id': goal.id,
                'activity_name': goal.activity.name,
                'target_days': goal.target_days,
                'current_streak': goal.current_streak,
                'longest_streak': goal.longest_streak,
                'progress': progress,
                'is_completed': goal.is_completed,
                'is_abstract': False
            })
        
        # 添加抽象目标数据
        for goal in abstract_goals:
            # 计算目标进度
            progress = min(100, int((goal.current_streak / goal.target_days) * 100)) if goal.target_days > 0 else 0
            
            # 获取关联的活动和坏习惯
            related_activities = await many_to_many_all_async(goal, 'related_activities')
            related_bad_habits = await many_to_many_all_async(goal, 'related_bad_habits')

            activity_names = [activity.name for activity in related_activities] if related_activities else []
            bad_habit_names = [habit.name for habit in related_bad_habits] if related_bad_habits else []
            
            goals_data.append({
                'id': goal.id,
                'goal_name': goal.goal_name,
                'description': goal.description,
                'target_days': goal.target_days,
                'current_streak': goal.current_streak,
                'longest_streak': goal.longest_streak,
                'progress': progress,
                'is_completed': goal.is_completed,
                'is_abstract': True,
                'related_activities': activity_names,
                'related_bad_habits': bad_habit_names
            })
        
        result_data = {
            'user': {
                'nickname': user.nickname,
                'check_in_count': user.check_in_count,
                'health_score': float(user.health_score),
                'current_streak': current_streak
            },
            'goals': goals_data,
            'top_activities': top_activities,
            'top_avoided_habits': top_avoided_habits,
            'top_failed_habits': top_failed_habits
        }

        try:
            cache.set(cache_key, json.dumps(result_data), timeout=300) # 缓存5分钟
            logger.info(f"[CACHE_SET] 💾 {cache_key} - 用户 {user_id} 的统计信息数据已缓存")
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 写入缓存 {cache_key} 失败: {str(e)}")

        return result_data
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        logger.error(f"异步查询用户统计失败: {str(e)}")
        return {"error": "查询用户统计失败"}

@async_bank_router.get("/abstract-goal/")
@api_timer("获取抽象目标列表")
async def abstract_goal_list_async(request):
    """异步获取用户的所有抽象目标列表"""
    try:
        user_id = request.user_id
        print(f"=== 异步获取抽象目标列表 - 用户ID: {user_id} ===")
        
        # 异步获取用户的所有抽象目标
        abstract_goals = await filter_async(
            model_class=AbstractGoal,
            user_id=user_id
        )
        
        goals_data = []
        for goal in abstract_goals:
            # 使用异步方法获取关联的活动和坏习惯
            related_activities = await many_to_many_all_async(goal, 'related_activities')
            related_bad_habits = await many_to_many_all_async(goal, 'related_bad_habits')

            activity_names = [activity.name for activity in related_activities] if related_activities else []
            bad_habit_names = [habit.name for habit in related_bad_habits] if related_bad_habits else []
            
            goals_data.append({
                'id': goal.id,
                'goal_name': goal.goal_name,
                'description': goal.description,
                'target_days': goal.target_days,
                'start_date': goal.start_date.isoformat(),
                'end_date': goal.end_date.isoformat() if goal.end_date else None,
                'is_completed': goal.is_completed,
                'current_streak': goal.current_streak,
                'longest_streak': goal.longest_streak,
                'related_activities': activity_names,
                'related_bad_habits': bad_habit_names,
                'created_at': goal.created_at.isoformat() if goal.created_at else None
            })
        
        print(f"✅ 成功获取 {len(goals_data)} 个抽象目标")
        return goals_data
        
    except Exception as e:
        print(f"❌ 获取抽象目标列表失败: {str(e)}")
        return {"status": "error", "message": str(e)}

@async_bank_router.post("/abstract-goal/")
@api_timer("创建抽象目标")
async def abstract_goal_create_async(request):
    """异步创建或更新抽象目标"""
    try:
        import json
        data = json.loads(request.body)
        user_id = request.user_id
        
        print(f"=== 异步创建/更新抽象目标 - 用户ID: {user_id} ===")
        print(f"请求数据: {data}")
        
        # 获取用户信息
        user = await get_async(UserInfo, id=user_id)
        if not user:
            return {"status": "error", "message": "用户不存在"}
        
        goal_name = data.get('goal_name')
        description = data.get('description', '')
        target_days = data.get('target_days', 30)
        start_date_str = data.get('start_date')
        related_activities = data.get('related_activities', [])
        related_bad_habits = data.get('related_bad_habits', [])
        
        # 获取失败设定参数
        max_consecutive_miss = data.get('max_consecutive_miss', 3)
        max_total_miss = data.get('max_total_miss', 7)
        max_bad_habit_times = data.get('max_bad_habit_times', 0)
        
        if not goal_name:
            return {"status": "error", "message": "请输入目标名称"}
        
        # 确保目标天数不低于7天
        if target_days < 7:
            target_days = 7
            
        # 确保失败设定合理
        if max_consecutive_miss > target_days // 3:
            max_consecutive_miss = target_days // 3
            
        if max_total_miss > target_days // 2:
            max_total_miss = target_days // 2
        
        # 解析开始日期
        if start_date_str:
            from django.utils import timezone as tz
            start_date = tz.datetime.strptime(start_date_str, '%Y-%m-%d').date()
        else:
            start_date = timezone.now().date()
        
        # 检查是否已有相同名称的未完成抽象目标
        existing_goals = await filter_async(
            AbstractGoal,
            user_id=user_id,
            goal_name=goal_name,
            is_completed=False,
            is_failed=False
        )
        existing_goal = existing_goals[0] if existing_goals else None
        
        if existing_goal:
            # 更新现有目标
            existing_goal.description = description
            existing_goal.target_days = target_days
            existing_goal.start_date = start_date
            existing_goal.max_consecutive_miss = max_consecutive_miss
            existing_goal.max_total_miss = max_total_miss
            existing_goal.max_bad_habit_times = max_bad_habit_times
            await save_async(existing_goal)
            
            # 清除和更新关联活动
            await many_to_many_clear_async(existing_goal, 'related_activities')
            await many_to_many_clear_async(existing_goal, 'related_bad_habits')

            if related_activities:
                activities = await filter_async(Activity, id__in=related_activities)
                if activities:
                    await many_to_many_add_async(existing_goal, 'related_activities', *activities)

            if related_bad_habits:
                bad_habits = await filter_async(Activity, id__in=related_bad_habits)
                if bad_habits:
                    await many_to_many_add_async(existing_goal, 'related_bad_habits', *bad_habits)
            
            goal = existing_goal
            message = '抽象目标已更新'
        else:
            # 创建新目标
            print(f"=== 准备创建新目标 ===")
            print(f"用户: {user}, 目标名称: {goal_name}")
            try:
                goal = await create_async(
                    AbstractGoal,
                    user=user,
                    goal_name=goal_name,
                    description=description,
                    target_days=target_days,
                    start_date=start_date,
                    max_consecutive_miss=max_consecutive_miss,
                    max_total_miss=max_total_miss,
                    max_bad_habit_times=max_bad_habit_times
                )
                print(f"✅ 目标创建成功: {goal.id}")
            except Exception as e:
                print(f"❌ 目标创建失败: {str(e)}")
                import traceback
                traceback.print_exc()
                raise
            
            # 添加关联活动
            if related_activities:
                activities = await filter_async(Activity, id__in=related_activities)
                if activities:
                    await many_to_many_add_async(goal, 'related_activities', *activities)

            if related_bad_habits:
                bad_habits = await filter_async(Activity, id__in=related_bad_habits)
                if bad_habits:
                    await many_to_many_add_async(goal, 'related_bad_habits', *bad_habits)
            
            message = '抽象目标已创建'
        
        # 获取关联的活动和坏习惯名称
        related_activities_objs = await many_to_many_all_async(goal, 'related_activities')
        related_bad_habits_objs = await many_to_many_all_async(goal, 'related_bad_habits')

        activity_names = [activity.name for activity in related_activities_objs]
        bad_habit_names = [habit.name for habit in related_bad_habits_objs]
        
        result = {
            'status': 'success',
            'message': message,
            'goal': {
                'id': goal.id,
                'goal_name': goal.goal_name,
                'description': goal.description,
                'target_days': goal.target_days,
                'start_date': goal.start_date.isoformat(),
                'end_date': goal.end_date.isoformat() if goal.end_date else None,
                'current_streak': goal.current_streak,
                'longest_streak': goal.longest_streak,
                'related_activities': activity_names,
                'related_bad_habits': bad_habit_names
            }
        }
        
        print(f"✅ 成功{message}: {goal.goal_name}")
        return result
        
    except Exception as e:
        print(f"❌ 创建/更新抽象目标失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"status": "error", "message": str(e)}

@async_bank_router.post("/user-card-month/")
@api_timer("获取月度打卡记录")
async def user_card_month_async(request):
    """异步查询用户月度打卡记录 - 替代UserCardMonthView"""
    try:
        import json
        data = json.loads(request.body)
        user_id = getattr(request, 'user_id', None)
        
        if not user_id:
            return {"error": "访问需要权限"}

        tz_sh = pytz.timezone('Asia/Shanghai') # 使用tz_sh变量
        now = timezone.datetime.now(tz_sh) # 使用tz_sh变量
        
        year = data.get('year', now.year)
        month = data.get('month', now.month)

        cache_key = f"user_card_month_async:{user_id}:{year}:{month}"
        try:
            cached_data_str = cache.get(cache_key)
            if cached_data_str:
                try:
                    cached_data = json.loads(cached_data_str)
                    logger.info(f"[CACHE_HIT] ✅ {cache_key} - 返回用户 {user_id} ({year}-{month}) 的缓存数据")
                    return cached_data
                except json.JSONDecodeError:
                    logger.warning(f"[CACHE_ERROR] ⚠️ {cache_key} - 解析缓存数据失败，将重新获取")
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 读取缓存 {cache_key} 失败: {str(e)}，将重新获取")
        
        print("Received data:", data)
        print("Processing for user:", user_id)
        
        start_date = timezone.datetime(year, month, 1).date()
        if month == 12:
            end_date = timezone.datetime(year + 1, 1, 1).date()
        else:
            end_date = timezone.datetime(year, month + 1, 1).date()
        
        # 异步获取用户卡片
        user_cards = await filter_async(UserCard, user=user_id, date__range=[start_date, end_date])
        
        # 异步获取用户活动
        user_activities = await filter_with_select_related_async(
            UserActivity,
            user_id=user_id,
            date__range=[start_date, end_date],
            completed=True,
            select_related=['activity']
        )

        # 异步获取用户坏习惯
        bad_habits = await filter_with_select_related_async(
            BadHabitRecord,
            user_id=user_id,
            date__range=[start_date, end_date],
            select_related=['habit']
        )
        
        # 构建活动映射
        date_activities = {}
        for ua in user_activities:
            date_str = ua.date.isoformat()
            if date_str not in date_activities:
                date_activities[date_str] = []
            date_activities[date_str].append({
                'id': ua.activity.id,
                'name': ua.activity.name,
                'is_completed': ua.completed,
                'is_bad_habit': False
            })
        
        for bh in bad_habits:
            date_str = bh.date.isoformat()
            if date_str not in date_activities:
                date_activities[date_str] = []
            date_activities[date_str].append({
                'id': bh.habit.id,
                'name': bh.habit.name,
                'is_completed': bh.avoided,
                'is_bad_habit': True,
                'avoided': bh.avoided
            })
        
        days_in_month = calendar.monthrange(year, month)[1]
        days_data = []
        
        for i in range(1, days_in_month + 1):
            date_obj = timezone.datetime(year, month, i).date()
            date_str = date_obj.isoformat()
            is_carded = any(card.date == date_obj for card in user_cards)
            
            day_data = {
                'day': i,
                'date': date_str,
                'isCarded': is_carded,
                'activities': date_activities.get(date_str, [])
            }
            days_data.append(day_data)
        
        print("Days data processed successfully")
        try:
            cache.set(cache_key, json.dumps(days_data), timeout=3600) # 缓存1小时
            logger.info(f"[CACHE_SET] 💾 {cache_key} - 用户 {user_id} ({year}-{month}) 的数据已缓存")
        except Exception as e:
            logger.error(f"[CACHE_ERROR] ⚠️ 写入缓存 {cache_key} 失败: {str(e)}")
            
        return days_data
            
    except Exception as e:
        import traceback
        traceback.print_exc()
        logger.error(f"异步查询月度打卡失败: {str(e)}")
        return {"error": "查询月度打卡失败"}

@async_bank_router.post("/day-activities/")
@api_timer("获取日期活动")
async def day_activities_async(request):
    """异步查询特定日期活动 - 替代DayActivitiesView"""
    try:
        import json
        data = json.loads(request.body)
        user_id = getattr(request, 'user_id', None)
        
        if not user_id:
            return {"error": "访问需要权限"}
        
        date_str = data.get('date')
        
        if date_str:
            date_obj = timezone.datetime.strptime(date_str, '%Y-%m-%d').date()
        else:
            date_obj = timezone.now().date()
            
        # 异步获取用户在该日期的活动记录
        user_activities = await filter_with_select_related_async(
            UserActivity,
            user_id=user_id,
            date=date_obj,
            select_related=['activity']
        )

        # 异步获取用户在该日期的坏习惯记录
        bad_habits = await filter_with_select_related_async(
            BadHabitRecord,
            user_id=user_id,
            date=date_obj,
            select_related=['habit']
        )
        
        # 构建返回数据
        activities_data = []
        for ua in user_activities:
            activities_data.append({
                'id': ua.id,
                'activity_id': ua.activity.id,
                'activity_name': ua.activity.name,
                'is_completed': ua.completed,
                'duration_minutes': ua.duration_minutes,
                'intensity': ua.intensity,
                'amount': ua.amount,  # 运动量信息
                'notes': ua.notes,
                'is_bad_habit': False,
                'is_custom': ua.activity.is_custom
            })
            
        for bh in bad_habits:
            activities_data.append({
                'id': bh.id,
                'activity_id': bh.habit.id,
                'activity_name': bh.habit.name,
                'is_completed': bh.avoided,
                'notes': bh.notes,
                'is_bad_habit': True,
                'avoided': bh.avoided,
                'intensity': bh.intensity,  # 添加程度字段
                'is_custom': bh.habit.is_custom
            })
            
        # 异步检查是否已经获得积分
        user_card = await filter_first_async(UserCard, user_id=user_id, date=date_obj)
        
        score_added = user_card.score_added if user_card else False
        
        return {
            'date': date_str,
            'activities': activities_data,
            'score_added': score_added
        }
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        logger.error(f"异步查询日期活动失败: {str(e)}")
        return {"error": "查询日期活动失败"}

@async_bank_router.post("/check-register-user/")
@api_timer("检查或注册用户")
async def check_or_register_user_async(request):
    """异步检查或注册用户 - 替代CheckOrRegisterUserView"""
    try:
        user_id = getattr(request, 'user_id', None)
        if not user_id:
            return JsonResponse({'status': 'error', 'message': '无法验证用户身份'}, status=401)
        
        # 解析请求数据
        data = json.loads(request.body.decode('utf-8'))
        user_info = data.get('userInfo', {})
        
        # 获取昵称并进行验证
        nickname = user_info.get('nickName', '')
        if len(nickname) > 8:
            return JsonResponse({
                'status': 'error',
                'code': 'INVALID_NICKNAME',
                'message': '昵称不能超过8个字'
            }, status=400)
        
        if nickname and not re.match(r'^[a-zA-Z0-9\u4e00-\u9fa5]+$', nickname):
            return JsonResponse({
                'status': 'error',
                'code': 'INVALID_NICKNAME',
                'message': '昵称只能包含字母、数字和汉字'
            }, status=400)
        
        # 昵称内容审核
        if nickname:
            try:
                content_reviewer = HuaweiContentReview(project_id=settings.HUAWEI_PROJECT_ID)
                review_result = await run_sync_function_async(content_reviewer.text_moderation, nickname)
                print(f"昵称审核结果: {json.dumps(review_result, ensure_ascii=False)}")
                
                # 检查审核结果
                if review_result.get('status') == 'success' and review_result.get('data', {}).get('suggestion') == 'block':
                    print(f"昵称 '{nickname}' 审核不通过")
                    return JsonResponse({
                        'status': 'error', 
                        'code': 'NICKNAME_BLOCKED',
                        'message': '昵称包含不当内容，请修改'
                    }, status=400)
            except Exception as e:
                # 审核服务异常时，记录日志但不阻止用户注册
                logger.warning(f"昵称审核服务异常: {str(e)}")
        
        # 使用异步方式获取或创建用户
        user, created = await get_or_create_async(
            UserInfo,
            defaults={
                'nickname': nickname,
                'avatar': user_info.get('avatarUrl', ''),
                'has_uploaded_info': 'nickName' in user_info and 'avatarUrl' in user_info
            },
            id=user_id
        )
        
        # 如果用户已存在，更新用户信息
        if not created:
            updated = False
            if nickname and user.nickname != nickname:
                user.nickname = nickname
                updated = True
            if user_info.get('avatarUrl') and user.avatar != user_info.get('avatarUrl'):
                user.avatar = user_info['avatarUrl']
                updated = True
            if updated and not user.has_uploaded_info:
                user.has_uploaded_info = True
            
            if updated:
                await save_async(user)
        
        print(f"User {'created' if created else 'updated'}: {user.nickname}, UserID: {user.id}")
        return JsonResponse({
            'status': 'success', 
            'message': '用户处理成功', 
            'is_member': user.is_member, 
            '昵称': user.nickname
        })
        
    except json.JSONDecodeError:
        return JsonResponse({'status': 'error', 'message': '无效的 JSON 数据'}, status=400)
    except Exception as e:
        logger.error(f"异步检查或注册用户失败: {str(e)}")
        print(f"Error: {str(e)}")
        return JsonResponse({'status': 'error', 'message': str(e)}, status=500)

@async_bank_router.post("/abstract-goal-record/")
@api_timer("抽象目标打卡")
async def abstract_goal_record_async(request):
    """异步抽象目标打卡 - 替代AbstractGoalRecordView"""
    try:
        import json
        data = json.loads(request.body)
        user_id = request.user_id

        print(f"=== 异步抽象目标打卡 - 用户ID: {user_id} ===")
        print(f"请求数据: {data}")

        # 获取用户信息
        user = await get_async(UserInfo, id=user_id)
        if not user:
            return {"status": "error", "message": "用户不存在"}

        goal_id = data.get('goal_id')
        is_completed = data.get('is_completed', True)
        notes = data.get('notes', '')
        date_str = data.get('date')

        if not goal_id:
            return {"status": "error", "message": "请选择目标"}

        # 异步获取目标
        try:
            goal = await get_async(AbstractGoal, id=goal_id, user=user)
        except Exception:
            return {"status": "error", "message": "目标不存在"}

        # 解析日期
        if date_str:
            date = timezone.datetime.strptime(date_str, '%Y-%m-%d').date()
        else:
            date = timezone.now().date()

        # 异步创建或更新打卡记录
        record, created = await get_or_create_async(
            AbstractGoalRecord,
            user=user,
            abstract_goal=goal,
            date=date,
            defaults={
                'is_completed': is_completed,
                'notes': notes
            }
        )

        # 如果记录已存在，更新它
        if not created:
            record.is_completed = is_completed
            record.notes = notes
            await save_async(record)

        # 异步更新目标的连续打卡记录
        await _update_abstract_goal_streak_async(user, goal, date)

        print(f"✅ 抽象目标打卡成功: 目标ID={goal.id}, 日期={date}")

        return {
            'status': 'success',
            'message': '打卡成功',
            'record': {
                'id': record.id,
                'goal_id': goal.id,
                'goal_name': goal.goal_name,
                'date': record.date.isoformat(),
                'is_completed': record.is_completed,
                'notes': record.notes
            }
        }

    except Exception as e:
        print(f"❌ 抽象目标打卡失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"status": "error", "message": str(e)}

async def _update_abstract_goal_streak_async(user, goal, current_date):
    """异步更新抽象目标的连续打卡记录"""
    try:
        # 检查昨天是否有打卡
        yesterday = current_date - timezone.timedelta(days=1)
        yesterday_record = await exists_async(
            AbstractGoalRecord,
            user=user,
            abstract_goal=goal,
            date=yesterday,
            is_completed=True
        )

        # 如果昨天有打卡，增加连续天数
        if yesterday_record:
            goal.current_streak += 1
        else:
            # 重置连续天数
            goal.current_streak = 1

        # 更新最长连续天数
        if goal.current_streak > goal.longest_streak:
            goal.longest_streak = goal.current_streak

        # 检查是否已完成目标
        if goal.current_streak >= goal.target_days:
            goal.is_completed = True

        await save_async(goal)
        print(f"✅ 目标连续记录已更新: 当前连续={goal.current_streak}, 最长连续={goal.longest_streak}")

    except Exception as e:
        print(f"❌ 更新抽象目标连续记录出错: {str(e)}")
        import traceback
        traceback.print_exc()

# 将每日中医题目API注册到异步银行路由器
async_bank_router.add_router("", daily_tcm_router)