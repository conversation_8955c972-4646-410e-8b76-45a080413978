#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能测试API配置文件
专门用于并发性能测试的API端点配置
排除了不需要测试的模块：RouterTest2、RouterTest3、修炼档案API、八字分析API、数据库健康监控API
"""

# 测试配置
BASE_URL = "http://127.0.0.1:8000"

# 从token文件读取测试token
def load_test_token():
    try:
        with open('test_token_user_2.txt', 'r') as f:
            return f.read().strip()
    except FileNotFoundError:
        print("⚠️ 未找到test_token_user_2.txt文件，将使用无token测试")
        return None

TEST_TOKEN = load_test_token()

# 准备认证头
AUTH_HEADERS = {}
if TEST_TOKEN:
    AUTH_HEADERS = {"Authorization": f"Bearer {TEST_TOKEN}"}

# 性能测试API配置 - 按业务重要性和使用频率分类
PERFORMANCE_TEST_APIS = {
    
    # ==================== 核心业务API ====================
    # 这些是最重要的业务API，需要重点测试性能
    
    # 1. 基础页面API
    "HomePage": {
        "url": f"{BASE_URL}/api/HomePage/",
        "method": "GET",
        "headers": {},
        "description": "首页静态内容（已优化缓存2小时）",
        "category": "基础页面",
        "priority": "高",
        "expected_qps": 500,
        "cache_enabled": True
    },
    
    # 2. 认证系统API - 只保留安全的Token刷新
    "Token刷新": {
        "url": f"{BASE_URL}/api/auth/refresh-token",
        "method": "POST",
        "headers": AUTH_HEADERS,
        "data": {"refresh_token": "test_refresh_token"},
        "description": "Token刷新接口（业务逻辑测试）",
        "category": "认证系统",
        "priority": "高",
        "expected_qps": 200,
        "cache_enabled": False,
        "risk_level": "低",
        "test_focus": "业务逻辑"
    },

    # 🚨 真正的高风险API已排除（这些才是不能测试的）:
    # - 微信登录API: 调用微信外部API，有限流和费用风险
    # - 手机号登录API: 可能触发安全机制和IP封禁
    # - AI分析API: 调用DeepSeek等大模型，高并发产生大量费用
    # - 支付相关API: 微信支付、支付宝等，涉及真实资金
    # - 短信API: 发送验证码，产生费用
    #
    # ✅ 以下是性能测试的重点（应该大力测试）:
    # - 计算密集型API: 问卷分析、证型计算等
    # - 数据库查询API: 各种列表查询、统计API
    # - 缓存优化API: 验证缓存命中率和性能提升
    
    # ==================== 异步银行API ====================
    # 用户核心功能，使用频率高
    
    "检查会员状态": {
        "url": f"{BASE_URL}/api/async-bank/checkmembership/",
        "method": "POST",
        "headers": AUTH_HEADERS,
        "description": "异步检查会员状态",
        "category": "银行系统",
        "priority": "高",
        "expected_qps": 300,
        "cache_enabled": True
    },
    
    "空请求": {
        "url": f"{BASE_URL}/api/bank/EmptyRequestView/",
        "method": "POST",
        "headers": AUTH_HEADERS,
        "description": "空请求保持会话",
        "category": "银行系统",
        "priority": "中",
        "expected_qps": 400,
        "cache_enabled": False
    },

    "抽象目标": {
        "url": f"{BASE_URL}/api/async-bank/abstract-goal/",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "获取抽象目标",
        "category": "银行系统",
        "priority": "中",
        "expected_qps": 150,
        "cache_enabled": True
    },
    
    "用户目标": {
        "url": f"{BASE_URL}/api/async-bank/user-goal/",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "异步用户目标查询",
        "category": "银行系统",
        "priority": "中",
        "expected_qps": 150,
        "cache_enabled": True
    },
    
    "活动列表": {
        "url": f"{BASE_URL}/api/async-bank/activity-list/",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "异步查询活动列表",
        "category": "银行系统",
        "priority": "中",
        "expected_qps": 200,
        "cache_enabled": True
    },
    
    "用户统计": {
        "url": f"{BASE_URL}/api/async-bank/user-stats/",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "异步查询用户统计信息",
        "category": "银行系统",
        "priority": "中",
        "expected_qps": 100,
        "cache_enabled": True
    },
    
    # ==================== 问卷系统API ====================
    # 核心业务功能，计算密集型
    
    "问卷详情": {
        "url": f"{BASE_URL}/api/questionnaire/v1/questionnaire/",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "获取问卷详情",
        "category": "问卷系统",
        "priority": "高",
        "expected_qps": 100,
        "cache_enabled": True
    },

    "用户信息": {
        "url": f"{BASE_URL}/api/questionnaire/v1/user_info/",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "获取用户信息",
        "category": "问卷系统",
        "priority": "中",
        "expected_qps": 120,
        "cache_enabled": True
    },

    "计算历史": {
        "url": f"{BASE_URL}/api/questionnaire/calculation_histories/",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "计算历史记录",
        "category": "问卷系统",
        "priority": "中",
        "expected_qps": 80,
        "cache_enabled": True
    },
    
    "检查问卷填写": {
        "url": f"{BASE_URL}/api/questionnaire/check_questionnaire_filled/",
        "method": "POST",
        "headers": AUTH_HEADERS,
        "data": {"questionnaire_id": 1},
        "description": "检查问卷填写状态",
        "category": "问卷系统",
        "priority": "中",
        "expected_qps": 150,
        "cache_enabled": True
    },
    
    "计算证型指标": {
        "url": f"{BASE_URL}/api/questionnaire/calculate_constitution_indicators/",
        "method": "POST",
        "headers": AUTH_HEADERS,
        "description": "计算证型指标（计算密集型 - 性能测试重点）",
        "category": "问卷系统",
        "priority": "高",
        "expected_qps": 50,
        "cache_enabled": True,
        "risk_level": "低",
        "test_focus": "计算性能"
    },

    "分析问卷结果": {
        "url": f"{BASE_URL}/api/questionnaire/analyze_questionnaire/",
        "method": "POST",
        "headers": AUTH_HEADERS,
        "description": "分析问卷结果（计算密集型 - 性能测试重点）",
        "category": "问卷系统",
        "priority": "高",
        "expected_qps": 30,
        "cache_enabled": True,
        "risk_level": "低",
        "test_focus": "计算性能"
    },
    
    # ==================== 每日中医题目API ====================
    # 高频访问的学习功能
    
    "每日中医题目": {
        "url": f"{BASE_URL}/api/async-bank/daily-tcm-questions/",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "获取每日中医题目",
        "category": "学习系统",
        "priority": "高",
        "expected_qps": 200,
        "cache_enabled": True
    },
    
    "提交答题得分": {
        "url": f"{BASE_URL}/api/async-bank/daily-quiz-score/",
        "method": "POST",
        "headers": AUTH_HEADERS,
        "data": {
            "score": 85,
            "total_questions": 10,
            "correct_answers": 8
        },
        "description": "提交每日答题得分（写入操作）",
        "category": "学习系统",
        "priority": "中",
        "expected_qps": 50,
        "cache_enabled": False,
        "risk_level": "中",
        "max_concurrent": 20,
        "max_requests": 50
    },
    
    "答题排名": {
        "url": f"{BASE_URL}/api/async-bank/quiz-ranking/",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "获取答题排名",
        "category": "学习系统",
        "priority": "中",
        "expected_qps": 150,
        "cache_enabled": True
    },
    
    # ==================== 预后系统API (RouterTest1) ====================
    # 核心医疗功能，计算复杂

    "热门关键词": {
        "url": f"{BASE_URL}/api/routertest1/prognosis/popular-keywords",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "获取搜索热门关键词",
        "category": "预后系统",
        "priority": "高",
        "expected_qps": 200,
        "cache_enabled": True
    },

    "搜索建议": {
        "url": f"{BASE_URL}/api/routertest1/prognosis/search-suggestions",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "获取搜索建议列表",
        "category": "预后系统",
        "priority": "高",
        "expected_qps": 180,
        "cache_enabled": True
    },

    "疗法分类列表": {
        "url": f"{BASE_URL}/api/routertest1/prognosis/therapy-classifications",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "获取疗法分类列表（有缓存）",
        "category": "预后系统",
        "priority": "高",
        "expected_qps": 200,
        "cache_enabled": True
    },

    "疗法列表": {
        "url": f"{BASE_URL}/api/routertest1/prognosis/therapies",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "获取所有疗法列表（有缓存）",
        "category": "预后系统",
        "priority": "高",
        "expected_qps": 150,
        "cache_enabled": True
    },

    # ==================== 论坛系统API ====================
    # 第二批测试：扩展功能API

    "论坛帖子列表": {
        "url": f"{BASE_URL}/api/doubao_aichat/chat/posts",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "获取论坛帖子列表",
        "category": "论坛系统",
        "priority": "中",
        "expected_qps": 100,
        "cache_enabled": True
    },

    "论坛版块列表": {
        "url": f"{BASE_URL}/api/doubao_aichat/chat/sections",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "获取论坛版块列表",
        "category": "论坛系统",
        "priority": "中",
        "expected_qps": 120,
        "cache_enabled": True
    },

    "用户帖子": {
        "url": f"{BASE_URL}/api/doubao_aichat/chat/user_posts",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "获取用户帖子",
        "category": "论坛系统",
        "priority": "中",
        "expected_qps": 80,
        "cache_enabled": True
    },

    # ==================== 医案系统API ====================
    # 第二批测试：扩展功能API

    "医案经验排行": {
        "url": f"{BASE_URL}/api/doubao_aichat/chat/medical_case_exp_ranking",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "医案经验值排行榜",
        "category": "医案系统",
        "priority": "中",
        "expected_qps": 90,
        "cache_enabled": True
    },

    "医案总分排行": {
        "url": f"{BASE_URL}/api/doubao_aichat/chat/medical_case_total_score_ranking",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "医案总得分排行榜",
        "category": "医案系统",
        "priority": "中",
        "expected_qps": 90,
        "cache_enabled": True
    },

    "医案用户统计": {
        "url": f"{BASE_URL}/api/doubao_aichat/chat/medical_case_user_stats",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "医案用户统计信息",
        "category": "医案系统",
        "priority": "中",
        "expected_qps": 70,
        "cache_enabled": True
    },

    "健康记录": {
        "url": f"{BASE_URL}/api/doubao_aichat/chat/get_health_records",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "获取健康记录",
        "category": "医案系统",
        "priority": "中",
        "expected_qps": 60,
        "cache_enabled": True
    },

    # ==================== 症状管理API ====================
    # 第二批测试：扩展功能API

    "所有症状": {
        "url": f"{BASE_URL}/api/tcmchat/get_all_symptoms/",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "获取所有症状",
        "category": "症状管理",
        "priority": "中",
        "expected_qps": 100,
        "cache_enabled": True
    },

    "自定义症状": {
        "url": f"{BASE_URL}/api/tcmchat/get_custom_symptoms/",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "获取自定义症状",
        "category": "症状管理",
        "priority": "中",
        "expected_qps": 80,
        "cache_enabled": True
    },

    # ==================== 第四批测试API ====================
    # 补充测试之前遗漏的API，主要包括：会员系统、认证系统、论坛系统、医案系统等

    # 会员系统API（未测试的）
    "记录目标": {
        "url": f"{BASE_URL}/api/bank/AbstractGoalRecordView/",
        "method": "POST",
        "headers": AUTH_HEADERS,
        "data": {"goal_id": 1, "progress": 50},
        "description": "记录抽象目标进度",
        "category": "会员系统",
        "priority": "中",
        "expected_qps": 60,
        "cache_enabled": False,
        "risk_level": "中",
        "max_concurrent": 15,
        "max_requests": 100
    },

    "自定义活动": {
        "url": f"{BASE_URL}/api/bank/CustomActivityView/",
        "method": "POST",
        "headers": AUTH_HEADERS,
        "data": {"activity_name": "测试活动", "description": "性能测试"},
        "description": "自定义活动创建",
        "category": "会员系统",
        "priority": "中",
        "expected_qps": 50,
        "cache_enabled": False,
        "risk_level": "中",
        "max_concurrent": 10,
        "max_requests": 50
    },

    "日活动": {
        "url": f"{BASE_URL}/api/bank/DayActivitiesView/",
        "method": "POST",
        "headers": AUTH_HEADERS,
        "data": {"date": "2025-08-03"},
        "description": "获取指定日期活动",
        "category": "会员系统",
        "priority": "中",
        "expected_qps": 80,
        "cache_enabled": True,
        "risk_level": "低"
    },

    "用户卡片": {
        "url": f"{BASE_URL}/api/bank/UserCardView/",
        "method": "POST",
        "headers": AUTH_HEADERS,
        "description": "获取用户卡片信息",
        "category": "会员系统",
        "priority": "中",
        "expected_qps": 100,
        "cache_enabled": True,
        "risk_level": "低"
    },

    "用户问答历史": {
        "url": f"{BASE_URL}/api/async-bank/user-quiz-history/",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "获取用户问答历史记录",
        "category": "学习系统",
        "priority": "中",
        "expected_qps": 90,
        "cache_enabled": True,
        "risk_level": "低"
    },

    # 论坛系统API（未测试的）
    "创建帖子": {
        "url": f"{BASE_URL}/api/doubao_aichat/chat/create_post",
        "method": "POST",
        "headers": AUTH_HEADERS,
        "data": {
            "title": "性能测试帖子",
            "content": "这是一个性能测试帖子",
            "section_id": 1
        },
        "description": "创建论坛帖子",
        "category": "论坛系统",
        "priority": "中",
        "expected_qps": 30,
        "cache_enabled": False,
        "risk_level": "中",
        "max_concurrent": 10,
        "max_requests": 50
    },

    "搜索帖子": {
        "url": f"{BASE_URL}/api/doubao_aichat/chat/search_posts",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "搜索论坛帖子",
        "category": "论坛系统",
        "priority": "中",
        "expected_qps": 70,
        "cache_enabled": True,
        "risk_level": "低"
    },

    "帖子评论": {
        "url": f"{BASE_URL}/api/doubao_aichat/chat/post_comments",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "获取帖子评论",
        "category": "论坛系统",
        "priority": "中",
        "expected_qps": 80,
        "cache_enabled": True,
        "risk_level": "低"
    },

    # 医案系统API（未测试的）
    "时间效率排行": {
        "url": f"{BASE_URL}/api/doubao_aichat/chat/medical_case_time_efficiency_ranking",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "医案时间效率排行榜",
        "category": "医案系统",
        "priority": "中",
        "expected_qps": 90,
        "cache_enabled": True,
        "risk_level": "低"
    },

    "提交游戏记录": {
        "url": f"{BASE_URL}/api/doubao_aichat/chat/medical_case_game_record",
        "method": "POST",
        "headers": AUTH_HEADERS,
        "data": {
            "case_id": 1,
            "score": 85,
            "time_spent": 300
        },
        "description": "提交医案游戏记录",
        "category": "医案系统",
        "priority": "中",
        "expected_qps": 40,
        "cache_enabled": False,
        "risk_level": "中",
        "max_concurrent": 10,
        "max_requests": 50
    },

    "增加经验值": {
        "url": f"{BASE_URL}/api/doubao_aichat/chat/medical_case_add_exp",
        "method": "POST",
        "headers": AUTH_HEADERS,
        "data": {"exp_points": 10},
        "description": "增加医案经验值",
        "category": "医案系统",
        "priority": "中",
        "expected_qps": 50,
        "cache_enabled": False,
        "risk_level": "中",
        "max_concurrent": 15,
        "max_requests": 100
    },

    "经验值历史": {
        "url": f"{BASE_URL}/api/doubao_aichat/chat/medical_case_exp_history",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "医案经验值历史记录",
        "category": "医案系统",
        "priority": "中",
        "expected_qps": 80,
        "cache_enabled": True,
        "risk_level": "低"
    },

    "游戏历史记录": {
        "url": f"{BASE_URL}/api/doubao_aichat/chat/medical_case_game_history",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "医案游戏历史记录",
        "category": "医案系统",
        "priority": "中",
        "expected_qps": 75,
        "cache_enabled": True,
        "risk_level": "低"
    },

    "案例排行榜": {
        "url": f"{BASE_URL}/api/doubao_aichat/chat/medical_case_cases_ranking",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "医案案例排行榜",
        "category": "医案系统",
        "priority": "中",
        "expected_qps": 85,
        "cache_enabled": True,
        "risk_level": "低"
    },

    "平均得分排行": {
        "url": f"{BASE_URL}/api/doubao_aichat/chat/medical_case_avg_score_ranking",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "医案平均得分排行榜",
        "category": "医案系统",
        "priority": "中",
        "expected_qps": 85,
        "cache_enabled": True,
        "risk_level": "低"
    },

    # 症状管理API（未测试的）
    "添加症状": {
        "url": f"{BASE_URL}/api/tcmchat/add_symptom/",
        "method": "POST",
        "headers": AUTH_HEADERS,
        "data": {
            "symptom_name": "测试症状",
            "description": "性能测试症状"
        },
        "description": "添加新症状",
        "category": "症状管理",
        "priority": "中",
        "expected_qps": 40,
        "cache_enabled": False,
        "risk_level": "中",
        "max_concurrent": 10,
        "max_requests": 50
    },

    "更新症状": {
        "url": f"{BASE_URL}/api/tcmchat/update_symptom/",
        "method": "PUT",
        "headers": AUTH_HEADERS,
        "data": {
            "symptom_id": 1,
            "symptom_name": "更新症状",
            "description": "更新的症状描述"
        },
        "description": "更新症状信息",
        "category": "症状管理",
        "priority": "中",
        "expected_qps": 35,
        "cache_enabled": False,
        "risk_level": "中",
        "max_concurrent": 10,
        "max_requests": 50
    },

    "添加自定义症状": {
        "url": f"{BASE_URL}/api/tcmchat/add_custom_symptom/",
        "method": "POST",
        "headers": AUTH_HEADERS,
        "data": {
            "symptom_name": "自定义测试症状",
            "description": "自定义症状描述"
        },
        "description": "添加自定义症状",
        "category": "症状管理",
        "priority": "中",
        "expected_qps": 45,
        "cache_enabled": False,
        "risk_level": "中",
        "max_concurrent": 10,
        "max_requests": 50
    },

    # 预后系统API（未测试的）
    "执行搜索": {
        "url": f"{BASE_URL}/api/routertest1/prognosis/search",
        "method": "POST",
        "headers": AUTH_HEADERS,
        "data": {"query": "头痛", "limit": 10},
        "description": "执行预后搜索",
        "category": "预后系统",
        "priority": "高",
        "expected_qps": 60,
        "cache_enabled": True,
        "risk_level": "低"
    },

    # 邀请系统API（未测试的）
    "我的邀请码": {
        "url": f"{BASE_URL}/api/invite_url/invite_api/my-code",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "获取我的邀请码",
        "category": "邀请系统",
        "priority": "中",
        "expected_qps": 70,
        "cache_enabled": True,
        "risk_level": "低"
    },

    "邀请记录": {
        "url": f"{BASE_URL}/api/invite_url/invite_api/records",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "获取邀请记录",
        "category": "邀请系统",
        "priority": "中",
        "expected_qps": 60,
        "cache_enabled": True,
        "risk_level": "低"
    },

    # 其他功能API（未测试的）
    "系统心跳": {
        "url": f"{BASE_URL}/api/ping/",
        "method": "GET",
        "headers": {},
        "description": "系统心跳检测",
        "category": "系统功能",
        "priority": "高",
        "expected_qps": 200,
        "cache_enabled": False,
        "risk_level": "低"
    },

    "干支查询": {
        "url": f"{BASE_URL}/api/tcmNLP/ganzhi/",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "干支查询功能",
        "category": "中医功能",
        "priority": "低",
        "expected_qps": 50,
        "cache_enabled": True,
        "risk_level": "低"
    },

    "获取当前季节": {
        "url": f"{BASE_URL}/api/tcmNLP/get_current_season/",
        "method": "GET",
        "headers": AUTH_HEADERS,
        "description": "获取当前季节信息",
        "category": "中医功能",
        "priority": "中",
        "expected_qps": 80,
        "cache_enabled": True,
        "risk_level": "低"
    },
}

# 按优先级分组的API配置
HIGH_PRIORITY_APIS = {k: v for k, v in PERFORMANCE_TEST_APIS.items() if v.get('priority') == '高'}
MEDIUM_PRIORITY_APIS = {k: v for k, v in PERFORMANCE_TEST_APIS.items() if v.get('priority') == '中'}

# 按类别分组的API配置
API_CATEGORIES = {}
for name, config in PERFORMANCE_TEST_APIS.items():
    category = config.get('category', '其他')
    if category not in API_CATEGORIES:
        API_CATEGORIES[category] = {}
    API_CATEGORIES[category][name] = config

# 安全并发配置
SAFE_CONCURRENT_USERS = [1, 3, 5, 10, 15]  # 安全的并发数配置
NORMAL_CONCURRENT_USERS = [1, 5, 10, 20, 40]  # 正常的并发数配置
STRESS_CONCURRENT_USERS = [1, 5, 10, 20, 40, 80]  # 压力测试并发数配置

# 风险等级配置
RISK_CONFIGS = {
    "低": {
        "concurrent_users": NORMAL_CONCURRENT_USERS,
        "max_requests": 200,
        "description": "低风险API，可以正常测试"
    },
    "中": {
        "concurrent_users": SAFE_CONCURRENT_USERS,
        "max_requests": 100,
        "description": "中风险API，需要控制并发"
    },
    "高": {
        "concurrent_users": [1, 2, 3],
        "max_requests": 20,
        "description": "高风险API，严格限制并发"
    }
}

# 按风险等级分组API
LOW_RISK_APIS = {k: v for k, v in PERFORMANCE_TEST_APIS.items() if v.get('risk_level', '低') == '低'}
MEDIUM_RISK_APIS = {k: v for k, v in PERFORMANCE_TEST_APIS.items() if v.get('risk_level') == '中'}
HIGH_RISK_APIS_DICT = {k: v for k, v in PERFORMANCE_TEST_APIS.items() if v.get('risk_level') == '高'}

# 测试场景配置
TEST_SCENARIOS = {
    "安全基础测试": LOW_RISK_APIS,
    "核心业务测试": {k: v for k, v in PERFORMANCE_TEST_APIS.items() if v.get('priority') == '高'},
    "完整安全测试": PERFORMANCE_TEST_APIS,
    "认证系统": API_CATEGORIES.get("认证系统", {}),
    "银行系统": API_CATEGORIES.get("银行系统", {}),
    "问卷系统": API_CATEGORIES.get("问卷系统", {}),
    "学习系统": API_CATEGORIES.get("学习系统", {}),
    "预后系统": API_CATEGORIES.get("预后系统", {}),
}

# 获取API的安全配置
def get_api_safe_config(api_name):
    """获取API的安全测试配置"""
    api_config = PERFORMANCE_TEST_APIS.get(api_name, {})
    risk_level = api_config.get('risk_level', '低')

    # 使用API自定义配置或风险等级默认配置
    max_concurrent = api_config.get('max_concurrent') or RISK_CONFIGS[risk_level]['concurrent_users'][-1]
    max_requests = api_config.get('max_requests') or RISK_CONFIGS[risk_level]['max_requests']
    concurrent_users = RISK_CONFIGS[risk_level]['concurrent_users']

    return {
        'concurrent_users': concurrent_users,
        'max_requests': max_requests,
        'max_concurrent': max_concurrent,
        'risk_level': risk_level
    }

# 导出配置
__all__ = [
    'PERFORMANCE_TEST_APIS',
    'HIGH_PRIORITY_APIS',
    'MEDIUM_PRIORITY_APIS',
    'API_CATEGORIES',
    'TEST_SCENARIOS',
    'LOW_RISK_APIS',
    'MEDIUM_RISK_APIS',
    'HIGH_RISK_APIS_DICT',
    'SAFE_CONCURRENT_USERS',
    'NORMAL_CONCURRENT_USERS',
    'STRESS_CONCURRENT_USERS',
    'RISK_CONFIGS',
    'get_api_safe_config',
    'BASE_URL',
    'AUTH_HEADERS'
]
